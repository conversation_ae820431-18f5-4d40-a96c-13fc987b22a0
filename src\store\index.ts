import { configureStore, combineReducers } from "@reduxjs/toolkit";
import sidebarReducer from "./features/sidebar";
import pinnedReducer from "./features/pinned";
import breadcrumbsReducer from "./features/breadcrumbs";
import localSettingsReducer from "./features/localSettings";
import globalSettingsReducer from "./features/globalSettings";
import authReducer from "./features/auth";
import navigationReducer from "./features/navigation";
import maskReducer from "./features/mask";
import trashReducer from "./features/trashcan";
import workingVersionReducer from "./features/workingVersion";
import templatesReducer from "./features/templates";
import tableUIReducer from "./features/tableUI";

// 👇 Step 1: Combine all your reducers
const appReducer = combineReducers({
  sidebar: sidebarReducer,
  pinned: pinnedReducer,
  breadcrumbs: breadcrumbsReducer,
  localSettings: localSettingsReducer,
  globalSettings: globalSettingsReducer,
  auth: authReducer,
  navigation: navigationReducer,
  mask: maskReducer,
  trash: trashReducer,
  workingVersion: workingVersionReducer,
  templatesStore: templatesReducer,
  tableUI: tableUIReducer,
});

// 👇 Step 2: Root reducer that resets state on logout
const rootReducer = (
  state: ReturnType<typeof appReducer> | undefined,
  action: any
) => {
  if (action.type === "auth/logout") {
    state = undefined;
  }
  return appReducer(state, action);
};

// 👇 Step 3: Create store using rootReducer
export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Infer types from the store
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
