import {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { AgGridReact } from "ag-grid-react";
import "./styles.css";
import { ColDef, GetRowIdParams, GridOptions } from "ag-grid-community";
import { CustomHeader } from "./CustomHeader";
import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import { Button } from "primereact/button";
import { Empty } from "antd";
import { localeText } from "./localeText";
import i18next from "i18next";
import { NoPermissionsModal } from "../../molecules";
import { CheckDetailsPermissions } from "./CheckDetailsPermissions";
import { EditSaveCancelRenderer } from "./EditSaveCancelRenderer";
import SwitchRenderer from "./SwitchRenderer";
import ImageRenderer from "./ImageRenderer";
import TextEditorRenderer from "./TextEditorRenderer";
import { TableLoadingSkeleton } from "./TableLoadingSkeleton";
import { uuidv4 } from "../../../utils";
import { withErrorBoundary } from "../../withErrorBoundary";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { toggleColumnWrap } from "../../../store/features/tableUI";

export type IAGColumns = {
  headerName: string;
  type?: "date";
  field: string;
  flex?: number;
  minWidth?: number;
  cellRenderer?: any;
  isAction?: boolean;
  width?: number;
};

interface Props {
  columns: any[];
  isError?: boolean;
  onRefresh?: () => void;
  withRefresh?: boolean;
  getRowClass?: any;
  hideEmpty?: boolean;
  onDownload?: (selected) => void;
  onGridReady?: (agGridRef: AgGridReact) => void;
  onColumnsDelete?: (key: string) => void;
  resetTrigger?: number;
  data: any[];
  enableCellSpan?: boolean;
  noSelect?: boolean;
  defaultSelected?: any[];
  onSelect?: (values: any[]) => void;
  loading?: boolean;
  multiplicity?: string;
  fullHeight?: boolean;
  excelFileName?: string;
  displaySaveCancel?: boolean;
  onCancelClick?: () => void;
  disableSave?: boolean;
  saveLoading?: boolean;
  onSaveClick?: (newColumns: string[], filters: any, sort, pinned) => void;
  detectChange?: () => void;
  noHeader?: boolean;
  height?: string;
  setColumnsRequest?: any;
  emptyMessage?: string;
  extra?: ReactNode;
  noDownload?: boolean;
  expandCondition?: (data) => void;
  isRowSelectable?: (data) => boolean;
  setPinned?: (pinned) => void;
  setFilters?: (filters) => void;
  initialFilters?: any;
  preventCellClickEdit?: boolean;
  setSort?: (data) => void;
  editable?: boolean;
  onRowsEdit?: (values) => void;
}

const MyTableBase = ({
  columns,
  data,
  enableCellSpan,
  setSort,
  noSelect,
  editable,
  onDownload,
  hideEmpty,
  isError,
  height,
  setColumnsRequest,
  fullHeight,
  onSelect,
  onColumnsDelete,
  defaultSelected,
  noDownload,
  loading,
  onRowsEdit,
  multiplicity,
  initialFilters,
  setPinned,
  displaySaveCancel,
  excelFileName,
  expandCondition,
  onCancelClick,
  noHeader,
  disableSave,
  saveLoading,
  onSaveClick,
  detectChange,
  emptyMessage,
  resetTrigger,
  getRowClass,
  extra,
  isRowSelectable,
  setFilters,
  withRefresh,
  onRefresh,
  onGridReady,
  preventCellClickEdit = false,
}: Props) => {
  const [selected, setSelected] = useState([]);
  const gridRef = useRef<AgGridReact>(null);
  const { t } = useTranslation();
  const [noPermissionPopup, setNoPermissionPopup] = useState(null);
  const dispatch = useDispatch();

  const [initialLoad, setInitialLoad] = useState(true);
  const initialSortAppliedRef = useRef(false);
  const initialDefaultSelectedRef = useRef(null);

  // Generate unique table ID for each table instance
  const tableId = useMemo(() => uuidv4(), []);

  // Get column wrap state from Redux for this specific table
  const allColumnWrapState = useSelector((state: RootState) => state.tableUI.columnWrapState);
  const columnWrapState = allColumnWrapState[tableId] || {};

  // Debug: Log state changes
  useEffect(() => {
    console.log('Redux state updated:', { tableId, columnWrapState, allColumnWrapState });
  }, [tableId, columnWrapState, allColumnWrapState]);

  // Helper function to toggle wrap state for a column
  const handleToggleColumnWrap = useCallback((fieldName: string) => {
    console.log('Toggle wrap called:', { tableId, fieldName });
    console.log('Current state before:', columnWrapState);
    dispatch(toggleColumnWrap({ tableId, fieldName }));
  }, [dispatch, tableId, columnWrapState]);

  // Helper function to get wrap state for a column
  const getColumnWrapState = useCallback((fieldName: string) => {
    return columnWrapState[fieldName] || false;
  }, [columnWrapState]);

  const stableSetNoPermissionPopup = useCallback((node) => {
    setNoPermissionPopup(node);
  }, []);

  const [columnDefs, setColumnDefs] = useState<ColDef[]>([]);

  const [gridData, setGridData] = useState([]);

  // Capture initial defaultSelected value only once
  useEffect(() => {
    if (
      initialDefaultSelectedRef.current === null &&
      defaultSelected?.length > 0
    ) {
      initialDefaultSelectedRef.current = defaultSelected;
    }
  }, [defaultSelected]);

  // Reset sort flag when data changes (new data loaded)
  useEffect(() => {
    initialSortAppliedRef.current = false;
    initialDefaultSelectedRef.current = null;
  }, [data]);

  useEffect(() => {
    setGridData((currentGridData) => {
      const dataMap = new Map(
        currentGridData.map((item) => [JSON.stringify(item), item])
      );

      let processedData = (data || []).map((newItem) => {
        const existingItem = dataMap.get(newItem.id);
        if (existingItem) {
          return { ...newItem, uuid: existingItem.uuid };
        }
        return { ...newItem, uuid: uuidv4() };
      });

      // Sort selected items to the top ONLY on initial load with defaultSelected
      // Use captured initial value to prevent re-sorting when parent components reassign defaultSelected
      if (
        initialDefaultSelectedRef.current?.length > 0 &&
        initialLoad &&
        !initialSortAppliedRef.current
      ) {
        const selectedIds = new Set(
          initialDefaultSelectedRef.current.map((item) => String(item.id))
        );

        processedData = processedData.sort((a, b) => {
          const aSelected =
            selectedIds.has(String(a.id)) || selectedIds.has(String(a.uuid));
          const bSelected =
            selectedIds.has(String(b.id)) || selectedIds.has(String(b.uuid));

          // Sort selected items to the top (bSelected - aSelected puts true values first)
          return (bSelected ? 1 : 0) - (aSelected ? 1 : 0);
        });

        // Mark that initial sort has been applied to prevent future re-sorting
        initialSortAppliedRef.current = true;
      }

      return processedData;
    });
  }, [data]);

  const editableRowData = useMemo(() => {
    if (!editable || !gridData) return gridData;
    return gridData.map((item, index) => ({ ...item, index }));
  }, [editable, gridData]);

  const optimalRowBuffer = useMemo(() => {
    const dataLength = data?.length || 0;
    if (dataLength > 10000) return 100;
    if (dataLength > 1000) return 75;
    if (dataLength > 100) return 50;
    return 20;
  }, [data?.length]);

  const tableHeight = useMemo(() => {
    if (fullHeight) return "100%";
    if (height) return height;
    if (loading || data?.length === 0) return 200;
    if (data?.length > 5) return 300;
    return "100%";
  }, [fullHeight, height, loading, data?.length]);

  const isRowMaster = useCallback(
    (data: any) =>
      data?.templateHasAttributes || (expandCondition && expandCondition(data)),
    [expandCondition]
  );
  const gridOptions = useMemo(
    () =>
    ({
      masterDetail: true,
      context: {
        setNoPermissionPopup: stableSetNoPermissionPopup,
      },
      detailCellRenderer: CheckDetailsPermissions,
      noRowsOverlayComponent: () => <p>{emptyMessage || "No data"}</p>,
      loadingOverlayComponent: TableLoadingSkeleton,
      suppressLoadingOverlay: true,
      ensureDomOrder: false,
      suppressRowClickSelection: true,
      suppressColumnVirtualisation: false,
      suppressRowVirtualisation: false,
      debounceVerticalScrollbar: true,
      rowHeight: 38, // Default row height
      getRowHeight: () => {
        // Check if any column is wrapped
        const hasWrappedColumn = Object.keys(columnWrapState).some(fieldName =>
          columnWrapState[fieldName] === true
        );
        return hasWrappedColumn ? undefined : 38; // Let ag-Grid calculate height for wrapped content
      },
    } as GridOptions),
    [emptyMessage, stableSetNoPermissionPopup, columnWrapState]
  );

  const getRowId = useCallback(
    (params: GetRowIdParams) => JSON.stringify(params.data),
    []
  );

  const processColumns = useCallback(
    (_columns: any[]) => {
      if (!_columns || _columns.length === 0) return [];

      return _columns
        .map((_column: any) => {
          if (_column?.isEditActions) {
            return {
              field: "actions",
              headerName: "",
              isAction: true,
              width: 100,
              cellRenderer: EditSaveCancelRenderer,
              singleClickEdit: true,
              sortable: false,
              headerComponent: null,
              filter: false,
            };
          }

          const isWrapped = getColumnWrapState(_column.field);

          return {
            ..._column,
            sortable: _column?.isAction ? false : true,
            autoHeight: isWrapped, // Use autoHeight for wrapped columns to handle dynamic content
            cellEditorSelector: (params: any) => {
              if (params.value === "true" || params.value === "false") {
                return {
                  component: SwitchRenderer,
                  params: { ...params },
                };
              }
              if (params?.data?.editType === "image") {
                return {
                  component: ImageRenderer,
                  params: { ...params },
                };
              }
              if (
                params?.data?.editType === "textEditor" &&
                !params?.column?.colDef?.isPlainTextEditor
              ) {
                return {
                  component: TextEditorRenderer,
                  params: {
                    ...params,
                  },
                };
              }
              return {
                component: "agTextCellEditor",
              };
            },
            headerName: t(_column.headerName),
            headerComponent: _column?.headerComponent
              ? _column.headerComponent
              : _column?.isAction
                ? null
                : (e: any) => (
                  <CustomHeader
                    {...e}
                    onColumnsDelete={() => onColumnsDelete?.(_column.field)}
                    onWrapToggle={handleToggleColumnWrap}
                    getColumnWrapState={getColumnWrapState}
                  />
                ),
            filter: _column?.isAction
              ? false
              : _column.type === "date"
                ? "agDateColumnFilter"
                : "agTextColumnFilter",
            filterParams: {
              buttons: ["reset", "apply"],
            },
          };
        })
        .filter(Boolean);
    },
    [t, onColumnsDelete, getColumnWrapState, handleToggleColumnWrap]
  );

  const rowSelectionConfig = useMemo(() => {
    if (noSelect) return undefined;
    const toMultiplicity = multiplicity?.split("..")[1];
    const mode = multiplicity
      ? toMultiplicity === "1"
        ? ("singleRow" as const)
        : ("multiRow" as const)
      : ("multiRow" as const);

    return {
      mode,
      isRowSelectable: (rowNode: any) => {
        // Use the existing isRowSelectable prop if provided
        if (isRowSelectable) {
          return isRowSelectable(rowNode.data);
        }
        // Check for disableCheck property to disable selection
        return !rowNode.data?.disableCheck;
      },
      hideDisabledCheckboxes: false, // Show disabled checkboxes instead of hiding them
    };
  }, [multiplicity, noSelect, isRowSelectable]);

  useEffect(() => {
    const processedColumns = processColumns(columns);

    // Note: In ag-grid v33+, selection columns are handled automatically by the rowSelection config
    // We no longer need to manually add the selection column as it's deprecated
    // The grid will automatically add the selection column based on rowSelection configuration

    if (
      Array.isArray(columns) &&
      columns.length > 0 &&
      processedColumns.length !== columns.length
    ) {
      setColumnDefs(processedColumns.filter(Boolean));
    } else {
      setColumnDefs(processedColumns);
    }
  }, [columns, i18next.language, processColumns, rowSelectionConfig, columnWrapState]);

  // Effect to apply CSS classes directly when wrap state changes
  useEffect(() => {
    console.log('Effect triggered, columnWrapState:', columnWrapState);
    if (!gridRef?.current?.api) {
      console.log('No grid API available');
      return;
    }

    // Apply CSS classes directly to all cells
    const allCells = document.querySelectorAll('.ag-cell');
    console.log('Found cells:', allCells.length);

    allCells.forEach(cell => {
      const colId = cell.getAttribute('col-id');
      console.log('Cell col-id:', colId, 'wrap state:', columnWrapState[colId]);
      if (colId && columnWrapState[colId]) {
        cell.classList.add('column-wrapped');
        console.log('Added column-wrapped to', colId);
      } else {
        cell.classList.remove('column-wrapped');
      }
    });
  }, [columnWrapState]);

  useEffect(() => {
    if (!initialLoad || !gridRef?.current?.api || columnDefs?.length === 0) {
      return;
    }

    try {
      gridRef?.current?.api?.setFilterModel(initialFilters);
      setInitialLoad(false);
    } catch {
      setInitialLoad(false);
    }
  }, [initialFilters, initialLoad, columnDefs]);

  const defaultColDef = useMemo<ColDef>(() => {
    return {
      unSortIcon: true,
      filter: true,
    };
  }, []);

  const safeSelectDefaultRows = useCallback(() => {
    if (!gridRef?.current?.api || !defaultSelected?.length) {
      return;
    }

    setTimeout(() => {
      const allNodes = [];
      gridRef.current.api.forEachNode((node) => {
        if (node.data) allNodes.push(node);
      });

      defaultSelected.forEach((_selected) => {
        let rowNode = gridRef?.current?.api?.getRowNode(_selected.id);

        if (!rowNode) {
          const matchingNodeByUuid = allNodes.find(
            (node) =>
              node.data.uuid && String(node.data.uuid) === String(_selected.id)
          );

          if (matchingNodeByUuid) {
            rowNode = matchingNodeByUuid;
          } else {
            const matchingNodeById = allNodes.find(
              (node) =>
                node.data.id && String(node.data.id) === String(_selected.id)
            );

            if (matchingNodeById) {
              rowNode = matchingNodeById;
            }
          }
        }

        if (rowNode) {
          rowNode.setSelected(true);
        }
      });
    }, 50);
  }, [defaultSelected]);

  const handleGridReady = useCallback(() => {
    if (defaultSelected?.length > 0) {
      safeSelectDefaultRows();
    }
    if (onGridReady) {
      onGridReady(gridRef.current);
    }

    // Apply initial wrap classes after grid is ready
    setTimeout(() => {
      const allCells = document.querySelectorAll('.ag-cell');
      allCells.forEach(cell => {
        const colId = cell.getAttribute('col-id');
        if (colId && columnWrapState[colId]) {
          cell.classList.add('column-wrapped');
        }
      });
    }, 100);
  }, [defaultSelected, safeSelectDefaultRows, onGridReady, columnWrapState]);

  useEffect(() => {
    if (gridRef?.current?.api && defaultSelected?.length > 0) {
      safeSelectDefaultRows();
    }
  }, [defaultSelected, data, safeSelectDefaultRows]);

  const safeRefreshCells = useCallback(() => {
    if (gridRef?.current?.api) {
      gridRef.current.api.refreshCells({ force: true });
    }
  }, []);

  const exportExcel = useCallback(() => {
    if (!gridRef?.current?.api) return;

    gridRef.current.api.exportDataAsCsv({
      onlySelected: true,
      fileName: `${excelFileName}.csv`,
    });
  }, [excelFileName]);

  useEffect(() => {
    if (typeof resetTrigger !== "number" || !gridRef?.current?.api) {
      return;
    }

    setInitialLoad(true);
    gridRef.current.api.resetColumnState();
    gridRef.current.api.setFilterModel(initialFilters || {});
    safeRefreshCells();
  }, [resetTrigger, safeRefreshCells, initialFilters]);

  const handleSaveClick = useCallback(() => {
    if (!gridRef?.current?.api) return;

    const api = gridRef.current.api;
    const newColumns = api.getAllGridColumns();

    const columnNames = newColumns
      .map((col) => col.getColDef().field)
      .filter(Boolean);

    const filterOptions = api.getFilterModel();
    const columnState = api.getColumnState();
    const sortModel = columnState
      .filter((col) => col.sort)
      .map(({ colId, sort }) => ({ colId, sort }));

    const pinnedColumns = newColumns
      .filter(
        (col) =>
          col.getPinned() && col.getColId() !== "ag-grid-selection-column"
      )
      .map((col) => col.getColId());

    onSaveClick?.(columnNames, filterOptions, sortModel, pinnedColumns);
  }, [onSaveClick]);

  const handleCancelClick = useCallback(() => {
    onCancelClick?.();
    if (!gridRef?.current?.api) return;

    gridRef.current.api.resetColumnState();
    safeRefreshCells();
    setInitialLoad(true);
  }, [onCancelClick, safeRefreshCells]);

  const gridEventHandlers = useMemo(() => {
    const handleCellEditingStarted = (params: any) => {
      if (
        params?.data?.editType === "textEditor" &&
        !params?.column?.colDef?.isPlainTextEditor
      ) {
        params.api.resetRowHeights();
        params.node.setRowHeight(300);
        params.api.onRowHeightChanged();
      }
    };

    const handleCellEditingStopped = (params: any) => {
      if (
        params?.data?.editType === "textEditor" &&
        !params?.column?.colDef?.isPlainTextEditor
      ) {
        params.api.resetRowHeights();
        params.node.setRowHeight(38);
        params.api.onRowHeightChanged();
      }
    };

    const handleFilterChanged = (props: any) => {
      if (!props?.api) return;

      props.api.refreshHeader();
      if (props?.source === "api") return;

      const api = gridRef.current?.api;
      if (!api) return;

      const filterOptions = api.getFilterModel();
      detectChange?.();
      setFilters?.(filterOptions);
    };

    const handleSortChanged = (e: any) => {
      if (!e?.api || e.source !== "api") return;

      const columnState = gridRef.current.api.getColumnState();
      const sortModel = columnState
        .filter((col) => col.sort)
        .map(({ colId, sort }) => ({ colId, sort }));
      detectChange?.();
      setSort?.(sortModel);
    };

    const handleColumnMoved = (e: any) => {
      if (!e?.api || e.source !== "uiColumnMoved") return;

      const newColumns = gridRef.current.api.getAllGridColumns();
      const columnNames = newColumns
        .map((col) => col.getColDef().field)
        .filter(Boolean);

      setColumnsRequest?.(columnNames);
      detectChange?.();
    };

    const handleColumnPinned = (params: any) => {
      if (params.source !== "api") return;

      detectChange?.();

      const cols = params.api.getAllGridColumns();
      const pinnedColumns: string[] = [];

      cols?.forEach((col: any) => {
        if (col.getPinned() && col.getColId() !== "ag-grid-selection-column") {
          pinnedColumns.push(col.getColId());
        }
      });

      setPinned?.(pinnedColumns);
    };

    const handleRowValueChanged = (e: any) => {
      onRowsEdit?.(e.data);
    };

    const handleSelectionChanged = (event: any) => {
      if (!event?.api || event.source === "api") return;

      const allVisibleNodes: any[] = [];
      event.api.forEachNodeAfterFilterAndSort((node: any) => {
        if (node.data) allVisibleNodes.push(node);
      });

      const selectedNodes = event.api.getSelectedNodes();
      // Note: With the new ag-grid v33 API, rows with disableCheck will automatically
      // have their checkboxes disabled and cannot be selected, so we don't need to
      // manually deselect them anymore

      const visibleIds = new Set(
        allVisibleNodes.map((node: any) => node.data.id)
      );
      const selectedVisibleNodes = selectedNodes.filter((node: any) =>
        visibleIds.has(node.data.id)
      );

      // If all visible rows are selected, but there are also hidden rows selected, this means "select all" was used after filtering.
      if (
        selectedVisibleNodes.length === allVisibleNodes.length &&
        selectedNodes.length > allVisibleNodes.length
      ) {
        // Deselect all, then select only visible
        event.api.deselectAll();
        allVisibleNodes.forEach((node: any) => node.setSelected(true));
        // Now update selection state
        const selectedData = allVisibleNodes.map((node: any) => node.data);
        onSelect?.(selectedData);
        setSelected(selectedData);
        return;
      }

      // Otherwise, just use the visible selected rows
      const selectedData = selectedVisibleNodes.map((node: any) => node.data);
      onSelect?.(selectedData);
      setSelected(selectedData);
    };

    const handleRowClassAssignment = (e: any) => {
      return `${getRowClass?.(e) || ""} ${e.data?.templateHasAttributes ? "" : "no-expand"
        }`;
    };

    return {
      handleCellEditingStarted,
      handleCellEditingStopped,
      handleFilterChanged,
      handleSortChanged,
      handleColumnMoved,
      handleColumnPinned,
      handleRowValueChanged,
      handleSelectionChanged,
      getRowClass: handleRowClassAssignment,
    };
  }, [
    detectChange,
    setPinned,
    setFilters,
    setSort,
    setColumnsRequest,
    onRowsEdit,
    onSelect,
    getRowClass,
  ]);

  return (
    <Container className="my-table" onClick={(e) => e.stopPropagation()}>
      {isError ? (
        <div className="empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="error-empty"
            description={t("Error in fetching data")}
          />
        </div>
      ) : !hideEmpty && !loading && data?.length === 0 ? (
        <div className="empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t(emptyMessage) || t("No Data")}
          />
        </div>
      ) : (
        <>
          {!noHeader && (
            <div className="header">
              {extra}

              {withRefresh && (
                <Button
                  type="button"
                  label={t("Refresh")}
                  icon="pi pi-sync"
                  className="primary-button"
                  onClick={() => onRefresh?.()}
                  rounded
                />
              )}

              {!noDownload && (
                <Button
                  type="button"
                  label={t("Download")}
                  icon="pi pi-download"
                  className="primary-button"
                  disabled={!selected?.length}
                  onClick={
                    onDownload ? () => onDownload(selected) : exportExcel
                  }
                  rounded
                />
              )}

              {displaySaveCancel && (
                <>
                  <Button
                    rounded
                    className="primary-button cancel-button"
                    type="button"
                    onClick={handleCancelClick}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    rounded
                    className="primary-button save-button"
                    type="button"
                    disabled={disableSave}
                    loading={saveLoading}
                    onClick={handleSaveClick}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </div>
          )}
          <Wrapper
            style={{
              height: tableHeight,
              width: "100%",
            }}
          >
            {loading ? (
              <TableLoadingSkeleton />
            ) : (
              <AgGridReact
                enableCellSpan={enableCellSpan}
                defaultColDef={defaultColDef}
                ref={gridRef}
                singleClickEdit={!preventCellClickEdit}
                localeText={i18next.language === "pl" ? localeText : null}
                rowBuffer={optimalRowBuffer}
                rowModelType="clientSide"
                getRowId={getRowId}
                suppressPaginationPanel={true}
                suppressHorizontalScroll={false}
                alwaysShowVerticalScroll={true}
                onGridReady={handleGridReady}
                onCellEditingStarted={
                  gridEventHandlers.handleCellEditingStarted
                }
                onCellEditingStopped={
                  gridEventHandlers.handleCellEditingStopped
                }
                onFilterChanged={gridEventHandlers.handleFilterChanged}
                rowData={editableRowData}
                onSortChanged={gridEventHandlers.handleSortChanged}
                getRowClass={gridEventHandlers.getRowClass}
                onColumnMoved={gridEventHandlers.handleColumnMoved}
                domLayout="normal"
                columnDefs={columnDefs}
                onColumnPinned={gridEventHandlers.handleColumnPinned}
                rowSelection={rowSelectionConfig}
                headerHeight={34}
                suppressDragLeaveHidesColumns
                animateRows={false}
                loading={loading}
                isRowMaster={isRowMaster}
                detailRowAutoHeight
                editType={editable ? "fullRow" : null}
                gridOptions={gridOptions}
                onRowValueChanged={gridEventHandlers.handleRowValueChanged}
                onSelectionChanged={gridEventHandlers.handleSelectionChanged}
              />
            )}
          </Wrapper>
        </>
      )}

      {!!noPermissionPopup && (
        <NoPermissionsModal
          visible={!!noPermissionPopup}
          onHide={() => {
            setTimeout(() => {
              setNoPermissionPopup(null);
            }, 100);
          }}
        />
      )}
    </Container>
  );
};

export const MyTable = withErrorBoundary(MyTableBase, "error.generic");

const Container = styled.div`
  height: 100%;
  width: 100%;
  & .ag-cell-inline-editing {
    height: 100%;
    width: 100%;
    border: 1px solid #80bfff;
    overflow: hidden;
  }

  & .ag-cell-editor {
    height: 100% !important;
  }

  & .tox {
    height: 100% !important;
    width: 100% !important;
  }

  & .tox-hugerte-aux,
  & .tox-dialog,
  & .tox-menu,
  & .tox-popup {
    z-index: 10001 !important;
  }
  & .ag-cell-inline-editing {
    border: none !important;
    box-shadow: none !important;

    & input {
      height: fit-content;
      width: 90%;
      margin-left: 12px;
      flex: unset;
    }
  }

  & .ag-cell-editor {
    & input {
      box-shadow: none;
    }
  }

  & .ag-selection-checkbox {
    align-self: flex-start !important;
    margin-top: 0 !important;
  }

  & .ag-checkbox-input-wrapper {
    margin-top: 0 !important;
  }
  & .ag-row-hover {
    background-color: white;
    &::before {
      background-color: white;
    }
  }
  & .ag-center-cols-viewport {
    min-height: auto;
  }

  & .ag-overlay {
    min-height: 200px;
    position: relative;
    margin-top: -42px;
  }
  & .empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  & .header {
    display: flex;
    gap: 8px;
    justify-content: right;
    margin-bottom: 11px;
  }
`;
const Wrapper = styled.div`
  & .highlight-row {
    background-color: #d2e6f6;
  }

  & .ag-cell-value {
    line-height: 1.6;
    padding-top: 8px;
  }

  & .ag-cell-wrapper {
    > .ag-group-contracted,
    .ag-group-expanded {
      padding-top: 2px;
      align-items: flex-start;
    }
  }

  & .no-expand .ag-row-group-indent-0 {
    margin-left: 0px;
  }
  & .expand-btn {
    cursor: pointer;
    & i {
      font-size: 15px;
    }
    &:hover i {
      color: var(--color-text);
    }
  }
  & .ag-actions {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    margin-top: 8px;
    gap: 8px;

    & .anticon-edit {
      color: var(--color-text);
    }

    & > div {
      width: 21px;
      border: 1px solid;
      height: 21px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: var(--color-text);
      border-radius: 5px;

      & > i {
        font-size: 12px;
      }
    }

    & .ag-danger {
      color: red;
    }
  }
`;
