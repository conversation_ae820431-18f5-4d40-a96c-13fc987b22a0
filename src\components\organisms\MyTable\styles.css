.ag-header-container {
  background-color: var(--color-light);
  color: var(--color-text);
  font-size: 13px;
  font-weight: 700;
}

.ag-header-viewport {
  background-color: var(--color-light);
}

.ag-pinned-left-header {
  background-color: var(--color-light);
  color: var(--color-text);
}

.ag-body .title-container {
  cursor: pointer;
  color: var(--color-text);
}

.ag-body .ant-dropdown-trigger:hover {
  text-decoration: underline;
}

.custom-header-container {
  display: flex;
  width: 100%;
}

.custom-header-container>p {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: gray;
  font-weight: 500;
}

.ag-cell {
  font-size: 12px;
}

.column-actions {
  display: flex;
  margin-left: auto;
  font-size: 14px;
  gap: 3px;
  align-items: center;
}

.column-actions i,
.column-actions span {
  cursor: pointer;
}

.ag-cell img {
  object-fit: contain;
}

/* Virtualization optimizations for all tables */
.ag-center-cols-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-horizontal-scroll {
  height: auto !important;
}

.ag-root-wrapper {
  overflow: hidden;
}

/* Ensure proper height calculation */
.ag-layout-normal {
  height: 100% !important;
}

/* Text wrapping support - higher specificity to override ag-Grid defaults */

/* Default behavior - ensure no-wrap is applied */
.ag-root .ag-cell .ag-cell-value {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  width: 100%;
}

/* Wrapped text behavior - higher specificity to ensure it overrides */
.ag-root .ag-cell.ag-cell-wrap-text .ag-cell-value {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: keep-all !important;
  overflow-wrap: break-word !important;
  overflow: visible !important;
  text-overflow: unset !important;
  line-height: 1.3 !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  padding: 8px 4px !important;
}

/* Handle HTML content wrapping without breaking layout */
.ag-root .ag-cell.ag-cell-wrap-text .ag-cell-value * {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: keep-all !important;
  overflow-wrap: break-word !important;
  display: inline !important;
  line-height: inherit !important;
}

/* Prevent layout shifts by maintaining consistent cell structure */
.ag-root .ag-cell.ag-cell-wrap-text {
  overflow: hidden !important;
  vertical-align: top !important;
}