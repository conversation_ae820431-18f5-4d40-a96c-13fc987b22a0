.ag-header-container {
  background-color: var(--color-light);
  color: var(--color-text);
  font-size: 13px;
  font-weight: 700;
}

.ag-header-viewport {
  background-color: var(--color-light);
}

.ag-pinned-left-header {
  background-color: var(--color-light);
  color: var(--color-text);
}

.ag-body .title-container {
  cursor: pointer;
  color: var(--color-text);
}

.ag-body .ant-dropdown-trigger:hover {
  text-decoration: underline;
}

.custom-header-container {
  display: flex;
  width: 100%;
}

.custom-header-container>p {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: gray;
  font-weight: 500;
}

.ag-cell {
  font-size: 12px;
}

.column-actions {
  display: flex;
  margin-left: auto;
  font-size: 14px;
  gap: 3px;
  align-items: center;
}

.column-actions i,
.column-actions span {
  cursor: pointer;
}

.ag-cell img {
  object-fit: contain;
}

/* Virtualization optimizations for all tables */
.ag-center-cols-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-horizontal-scroll {
  height: auto !important;
}

.ag-root-wrapper {
  overflow: hidden;
}

/* Ensure proper height calculation */
.ag-layout-normal {
  height: 100% !important;
}

/* Text wrapping support - simple and effective */

/* Default behavior - no wrap */
.ag-cell .ag-cell-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* Wrapped text behavior - force everything to wrap */
.ag-cell.column-wrapped .ag-cell-value,
.ag-cell.column-wrapped .ag-cell-value * {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: normal !important;
  overflow-wrap: break-word !important;
  overflow: visible !important;
  text-overflow: unset !important;
  line-height: 1.4 !important;
}

/* Handle flex elements in wrapped cells */
.ag-cell.column-wrapped {
  flex-wrap: wrap !important;
  align-items: flex-start !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

/* Ensure wrapped content doesn't break table layout */
.ag-cell.column-wrapped .ag-cell-value {
  max-width: 100% !important;
  box-sizing: border-box !important;
}