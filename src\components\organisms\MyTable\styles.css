.ag-header-container {
  background-color: var(--color-light);
  color: var(--color-text);
  font-size: 13px;
  font-weight: 700;
}

.ag-header-viewport {
  background-color: var(--color-light);
}

.ag-pinned-left-header {
  background-color: var(--color-light);
  color: var(--color-text);
}

.ag-body .title-container {
  cursor: pointer;
  color: var(--color-text);
}

.ag-body .ant-dropdown-trigger:hover {
  text-decoration: underline;
}

.custom-header-container {
  display: flex;
  width: 100%;
}

.custom-header-container>p {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: gray;
  font-weight: 500;
}

.ag-cell {
  font-size: 12px;
}

.column-actions {
  display: flex;
  margin-left: auto;
  font-size: 14px;
  gap: 3px;
  align-items: center;
}

.column-actions i,
.column-actions span {
  cursor: pointer;
}

.ag-cell img {
  object-fit: contain;
}

/* Virtualization optimizations for all tables */
.ag-center-cols-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-horizontal-scroll {
  height: auto !important;
}

.ag-root-wrapper {
  overflow: hidden;
}

/* Ensure proper height calculation */
.ag-layout-normal {
  height: 100% !important;
}

/* Text wrapping support */
.ag-cell {
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
}

/* Default no-wrap behavior for cells */
.ag-cell .ag-cell-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* Wrapped text behavior - override when wrapText is enabled */
.ag-cell-wrap-text .ag-cell-value {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  overflow: visible;
  text-overflow: unset;
  line-height: 1.4;
}

/* Ensure proper cell height for wrapped content */
.ag-row-auto-height .ag-cell {
  align-items: flex-start;
  padding-top: 8px;
  padding-bottom: 8px;
}

/* Handle HTML content wrapping */
.ag-cell-wrap-text .ag-cell-value * {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}