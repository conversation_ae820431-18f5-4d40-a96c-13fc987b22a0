import React from 'react';
import { MyTable } from './index';

// Test component to validate wrap toggle functionality
const WrapToggleTest = () => {
  const testData = [
    {
      id: 1,
      shortText: 'Short',
      longText: 'This is a very long text that should wrap when the wrap toggle is enabled. It contains multiple words and should demonstrate the wrapping functionality clearly without breaking words inappropriately.',
      htmlContent: '<p>This is <strong>HTML content</strong> with <em>formatting</em> that should also wrap properly when the toggle is enabled.</p>',
      urlExample: 'https://example.com/very-long-url-that-might-need-wrapping/with/multiple/segments',
    },
    {
      id: 2,
      shortText: 'Another',
      longText: 'Another long text example with even more content to test the wrapping behavior across different rows and ensure consistency. Words should wrap at boundaries, not break in the middle.',
      htmlContent: '<div>HTML with <span style="color: red;">styled content</span> and <a href="#">links</a> that should wrap nicely without distortion.</div>',
      urlExample: 'mailto:<EMAIL>',
    },
  ];

  const testColumns = [
    {
      headerName: 'ID',
      field: 'id',
      width: 80,
    },
    {
      headerName: 'Short Text',
      field: 'shortText',
      width: 120,
    },
    {
      headerName: 'Long Text',
      field: 'longText',
      width: 200,
    },
    {
      headerName: 'HTML Content',
      field: 'htmlContent',
      width: 250,
      cellRenderer: (params: any) => {
        return <div dangerouslySetInnerHTML={{ __html: params.value }} />;
      },
    },
    {
      headerName: 'URL Example',
      field: 'urlExample',
      width: 200,
    },
  ];

  return (
    <div style={{ height: '400px', width: '100%', padding: '20px' }}>
      <h3>Wrap Toggle Test</h3>
      <p>Click the wrap/unwrap icons (📄/➖) in column headers to test the functionality.</p>
      <p>Note: Words wrap at boundaries without breaking, URLs and long content wrap properly.</p>
      <MyTable
        columns={testColumns}
        data={testData}
        noSelect={true}
        noDownload={true}
        noHeader={false}
      />
    </div>
  );
};

export default WrapToggleTest;
