import { createSlice } from "@reduxjs/toolkit";

export interface TableUIState {
  columnWrapState: Record<string, Record<string, boolean>>; // tableId -> fieldName -> isWrapped
}

const initialState: TableUIState = {
  columnWrapState: {},
};

export const tableUISlice = createSlice({
  name: "tableUI",
  initialState,
  reducers: {
    toggleColumnWrap: (state, action) => {
      const { tableId, fieldName } = action.payload;
      if (!state.columnWrapState[tableId]) {
        state.columnWrapState[tableId] = {};
      }
      state.columnWrapState[tableId][fieldName] = !state.columnWrapState[tableId][fieldName];
    },
    setColumnWrap: (state, action) => {
      const { tableId, fieldName, isWrapped } = action.payload;
      if (!state.columnWrapState[tableId]) {
        state.columnWrapState[tableId] = {};
      }
      state.columnWrapState[tableId][fieldName] = isWrapped;
    },
    resetColumnWrapState: (state, action) => {
      const tableId = action.payload;
      if (tableId) {
        delete state.columnWrapState[tableId];
      } else {
        state.columnWrapState = {};
      }
    },
  },
});

export const { toggleColumnWrap, setColumnWrap, resetColumnWrapState } = tableUISlice.actions;

export default tableUISlice.reducer;
