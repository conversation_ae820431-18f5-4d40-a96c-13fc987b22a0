import { createSlice } from "@reduxjs/toolkit";

export interface TableUIState {
  columnWrapState: Record<string, boolean>; // fieldName -> isWrapped
}

const initialState: TableUIState = {
  columnWrapState: {},
};

export const tableUISlice = createSlice({
  name: "tableUI",
  initialState,
  reducers: {
    toggleColumnWrap: (state, action) => {
      const fieldName = action.payload;
      state.columnWrapState[fieldName] = !state.columnWrapState[fieldName];
    },
    setColumnWrap: (state, action) => {
      const { fieldName, isWrapped } = action.payload;
      state.columnWrapState[fieldName] = isWrapped;
    },
    resetColumnWrapState: (state) => {
      state.columnWrapState = {};
    },
  },
});

export const { toggleColumnWrap, setColumnWrap, resetColumnWrapState } = tableUISlice.actions;

export default tableUISlice.reducer;
